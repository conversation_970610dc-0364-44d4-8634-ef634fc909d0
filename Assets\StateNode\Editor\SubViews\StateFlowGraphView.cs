using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEditor;
using UnityEngine.UIElements;
using UnityEditor.UIElements;
using UnityEditor.Experimental.GraphView;
using StateScript;

namespace StateScript.Editor
{
    /// <summary>
    /// GraphView implementation for StateFlow node editing
    /// </summary>
    public class StateFlowGraphView : GraphView
    {
        public event Action<StateNodeView> OnNodeViewSelected;

        private StateFlowEditorWindow _editorWindow;
        private StateFlow _stateFlow;
        private Dictionary<UniqueId, StateNodeView> _nodeViews = new Dictionary<UniqueId, StateNodeView>();
        private GridBackground _gridBackground;
        
        public StateFlowGraphView(StateFlowEditorWindow editorWindow)
        {
            _editorWindow = editorWindow;
            
            // Add grid background
            _gridBackground = new GridBackground();
            _gridBackground.name = "Grid";
            Add(_gridBackground);
            _gridBackground.StretchToParentSize();
            _gridBackground.SendToBack();
            
            // Configure GraphView
            SetupZoom(ContentZoomer.DefaultMinScale, ContentZoomer.DefaultMaxScale);
            
            this.AddManipulator(new ContentDragger());
            this.AddManipulator(new SelectionDragger());
            //this.AddManipulator(new RectangleSelector());
            //this.AddManipulator(new ClickSelector());
            this.AddManipulator(new StateScript.Editor.EdgeManipulator());

            // Handle selection changes
            graphViewChanged += OnGraphViewChanged;
            //this.nodeCreationRequest += OnNodeCreationRequest;

            // Handle selection changes for inspector
            this.RegisterCallback<MouseDownEvent>(OnMouseDown);
        }
        
        public void LoadStateFlow(StateFlow stateFlow)
        {
            _stateFlow = stateFlow;
            
            // Clear existing content
            ClearGraph();
            
            if (stateFlow == null)
                return;
            
            // Create node views
            foreach (var node in stateFlow.Nodes)
            {
                CreateNodeView(node);
            }
            
            // Create connections
            foreach (var connection in stateFlow.Connections)
            {
                CreateConnectionView(connection);
            }
        }
        
        private void ClearGraph()
        {
            // Remove all nodes and edges
            foreach (var node in nodes.ToList())
            {
                RemoveElement(node);
            }
            
            foreach (var edge in edges.ToList())
            {
                RemoveElement(edge);
            }
            
            _nodeViews.Clear();
        }
        
        private void CreateNodeView(StateNode node)
        {
            var nodeView = StateNodeView.Create(node);
            nodeView.SetPosition(new Rect(node.EditorProperty.Position, Vector2.zero));

            AddElement(nodeView);
            _nodeViews[node.Property.Id] = nodeView;
        }
        
        private void CreateConnectionView(StateConnection connection)
        {
            // Find the ports
            StatePort outputPort = null;
            StatePort inputPort = null;
            
            foreach (var nodeView in _nodeViews.Values)
            {
                var foundOutputPort = nodeView.StateNode.Property.Ports.FirstOrDefault(p => p.Id == connection.OutputPortId);
                if (foundOutputPort != null)
                {
                    outputPort = foundOutputPort;
                }
                
                var foundInputPort = nodeView.StateNode.Property.Ports.FirstOrDefault(p => p.Id == connection.InputPortId);
                if (foundInputPort != null)
                {
                    inputPort = foundInputPort;
                }
            }
            
            if (outputPort != null && inputPort != null)
            {
                // Find the corresponding port views
                var outputNodeView = _nodeViews.Values.FirstOrDefault(nv => nv.StateNode.Property.Id == outputPort.UniqueId);
                var inputNodeView = _nodeViews.Values.FirstOrDefault(nv => nv.StateNode.Property.Id == inputPort.UniqueId);
                
                if (outputNodeView != null && inputNodeView != null)
                {
                    var outputPortView = outputNodeView.GetPortView(outputPort.Id);
                    var inputPortView = inputNodeView.GetPortView(inputPort.Id);
                    
                    if (outputPortView != null && inputPortView != null)
                    {
                        var edge = outputPortView.ConnectTo(inputPortView);
                        AddElement(edge);
                    }
                }
            }
        }
        


        private List<System.Type> GetCustomNodeTypes()
        {
            var customTypes = new List<System.Type>();

            try
            {
                // Get all registered node types from the factory
                var registeredTypes = StateNodeFactory.GetRegisteredTypes();
                var coreTypes = new HashSet<string> { "StateActionNode", "StateConditionNode", "StateListenerNode" };

                foreach (var typeName in registeredTypes)
                {
                    if (!coreTypes.Contains(typeName))
                    {
                        // Try to find the actual type
                        var assemblies = System.AppDomain.CurrentDomain.GetAssemblies();
                        foreach (var assembly in assemblies)
                        {
                            var type = assembly.GetType(typeName) ?? assembly.GetType($"StateScript.{typeName}");
                            if (type != null && typeof(StateNode).IsAssignableFrom(type))
                            {
                                customTypes.Add(type);
                                break;
                            }
                        }
                    }
                }
            }
            catch (System.Exception e)
            {
                Debug.LogWarning($"Failed to get custom node types: {e.Message}");
            }

            return customTypes;
        }

        private void CreateCustomNode(System.Type nodeType, Vector2 screenPosition)
        {
            if (_stateFlow == null)
                return;

            try
            {
                var worldPosition = contentViewContainer.WorldToLocal(screenPosition);

                var node = (StateNode)System.Activator.CreateInstance(nodeType);
                node.EditorProperty.Position = worldPosition;
                node.EditorProperty.Name = new Fixed32String(nodeType.Name);

                _stateFlow.AddNode(node);
                CreateNodeView(node);

                // Mark as dirty
                _editorWindow.OnGraphChanged();
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Failed to create custom node {nodeType.Name}: {e.Message}");
            }
        }
        
        private void CreateNode<T>(Vector2 screenPosition) where T : StateNode, new()
        {
            if (_stateFlow == null)
                return;
            
            var worldPosition = contentViewContainer.WorldToLocal(screenPosition);
            
            var node = new T();
            node.EditorProperty.Position = worldPosition;
            node.EditorProperty.Name = new Fixed32String(typeof(T).Name);
            
            _stateFlow.AddNode(node);
            CreateNodeView(node);

            // Mark as dirty
            _editorWindow.OnGraphChanged();
        }
        
        private GraphViewChange OnGraphViewChanged(GraphViewChange graphViewChange)
        {
            if (_stateFlow == null)
                return graphViewChange;
            
            // Handle node deletions
            if (graphViewChange.elementsToRemove != null)
            {
                foreach (var element in graphViewChange.elementsToRemove)
                {
                    if (element is StateNodeView nodeView)
                    {
                        _stateFlow.RemoveNode(nodeView.StateNode.Property.Id);
                        _nodeViews.Remove(nodeView.StateNode.Property.Id);
                    }
                    else if (element is Edge edge)
                    {
                        // Handle connection removal
                        var outputPort = edge.output as StatePortView;
                        var inputPort = edge.input as StatePortView;
                        
                        if (outputPort != null && inputPort != null)
                        {
                            _stateFlow.RemoveConnection(outputPort.StatePort.Id, inputPort.StatePort.Id);
                        }
                    }
                }
            }
            
            // Handle edge creation
            if (graphViewChange.edgesToCreate != null)
            {
                foreach (var edge in graphViewChange.edgesToCreate)
                {
                    Debug.Log("-----Requesting Create Edge");
                    var outputPort = edge.output as StatePortView;
                    var inputPort = edge.input as StatePortView;
                    
                    if (outputPort != null && inputPort != null)
                    {
                        _stateFlow.AddConnection(outputPort.StatePort.Id, inputPort.StatePort.Id);
                    }
                }
            }
            
            // Handle node moves
            if (graphViewChange.movedElements != null)
            {
                foreach (var element in graphViewChange.movedElements)
                {
                    if (element is StateNodeView nodeView)
                    {
                        nodeView.StateNode.EditorProperty.Position = nodeView.GetPosition().position;
                    }
                }
            }
            
            // Mark as dirty for any changes
            _editorWindow.OnGraphChanged();

            return graphViewChange;
        }

        private void OnMouseDown(MouseDownEvent evt)
        {
            // Check if we clicked on empty space (not on a node)
            if (evt.target == this)
            {
                // Clear selection and notify inspector
                ClearSelection();
                OnNodeViewSelected?.Invoke(null);
            }
        }

        public override void AddToSelection(ISelectable selectable)
        {
            base.AddToSelection(selectable);
            UpdateInspectorSelection();
        }

        public override void RemoveFromSelection(ISelectable selectable)
        {
            base.RemoveFromSelection(selectable);
            UpdateInspectorSelection();
        }

        public override void ClearSelection()
        {
            base.ClearSelection();
            UpdateInspectorSelection();
        }

        private void UpdateInspectorSelection()
        {
            // Find the first selected node view
            StateNodeView selectedNodeView = null;
            foreach (var element in selection)
            {
                if (element is StateNodeView nodeView)
                {
                    selectedNodeView = nodeView;
                    break;
                }
            }

            OnNodeViewSelected?.Invoke(selectedNodeView);
        }

        public void SetGridVisible(bool visible)
        {
            if (_gridBackground != null)
            {
                _gridBackground.style.display = visible ? DisplayStyle.Flex : DisplayStyle.None;
                _gridBackground.visible = visible;
            }
        }

        public bool IsGridVisible()
        {
            return _gridBackground != null && _gridBackground.style.display == DisplayStyle.Flex && _gridBackground.visible;
        }

        public override List<Port> GetCompatiblePorts(Port startPort, NodeAdapter nodeAdapter)
        {
            var compatiblePorts = new List<Port>();
            
            foreach (var port in ports.ToList())
            {
                if (startPort != port && 
                    startPort.node != port.node && 
                    startPort.direction != port.direction)
                {
                    compatiblePorts.Add(port);
                }
            }
            
            return compatiblePorts;
        }

        // Handle connection drops - show node creation menu when connection fails
        public override EventPropagation DeleteSelection()
        {
            return base.DeleteSelection();
        }

        public override void BuildContextualMenu(ContextualMenuPopulateEvent evt)
        {
            // Clear default menu items first
            evt.menu.ClearItems();

            // Show node creation menu when right-clicking on empty space or when dropping a connection
            if ((evt.target is StateFlowGraphView && selection.Count == 0) || evt.target == this)
            {
                var mousePos = evt.localMousePosition;

                evt.menu.AppendAction("Action Node", (a) => CreateNode<StateActionNode>(mousePos));
                evt.menu.AppendAction("Condition Node", (a) => CreateNode<StateConditionNode>(mousePos));
                evt.menu.AppendAction("Listener Node", (a) => CreateNode<StateListenerNode>(mousePos));

                // Add separator
                evt.menu.AppendSeparator();

                // Check for custom node types
                var customNodeTypes = GetCustomNodeTypes();
                if (customNodeTypes.Count > 0)
                {
                    foreach (var nodeType in customNodeTypes)
                    {
                        var typeName = nodeType.Name;
                        evt.menu.AppendAction($"Custom/{typeName}", (a) => CreateCustomNode(nodeType, mousePos));
                    }
                }
                else
                {
                    // Show helpful message when no custom nodes are available
                    evt.menu.AppendAction("Custom/No custom nodes available", null, DropdownMenuAction.Status.Disabled);
                }
            }
            else if (selection.Count > 0)
            {
                // Add delete option for selected nodes
                evt.menu.AppendAction("Delete", (a) => DeleteSelection());
            }
        }
    }
}
