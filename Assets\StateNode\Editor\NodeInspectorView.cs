using UnityEngine;
using UnityEngine.UIElements;
using UnityEditor;
using UnityEditor.UIElements;
using StateScript;

namespace StateScript.Editor
{
    /// <summary>
    /// Inspector view that displays properties for selected nodes
    /// Uses the node's OnInspectorGUI method for custom UI
    /// </summary>
    public class NodeInspectorView : VisualElement
    {
        private Label _titleLabel;
        private VisualElement _contentContainer;
        private StateNodeView _selectedNodeView;
        
        public NodeInspectorView()
        {
            CreateUI();
            SetupAsOverlay();
        }
        
        private void CreateUI()
        {
            style.backgroundColor = new Color(0.25f, 0.25f, 0.25f, 1f);
            style.paddingTop = 5;
            style.paddingBottom = 5;
            style.paddingLeft = 5;
            style.paddingRight = 5;
            style.marginTop = 10;
            
            // Title
            _titleLabel = new Label("Inspector");
            _titleLabel.style.fontSize = 14;
            _titleLabel.style.unityFontStyleAndWeight = FontStyle.Bold;
            _titleLabel.style.marginBottom = 5;
            Add(_titleLabel);
            
            // Content container
            _contentContainer = new VisualElement();
            Add(_contentContainer);
            
            ShowNoSelection();
        }
        
        private void SetupAsOverlay()
        {
            // Position as floating overlay
            style.position = Position.Absolute;
            style.top = 10;
            style.right = 10;
            style.width = 300;
            style.maxHeight = Length.Percent(80);
            
            // Styling for overlay appearance
            style.backgroundColor = new Color(0.25f, 0.25f, 0.25f, 0.95f);
            style.borderTopLeftRadius = 8;
            style.borderTopRightRadius = 8;
            style.borderBottomLeftRadius = 8;
            style.borderBottomRightRadius = 8;
            style.borderTopColor = new Color(0.4f, 0.4f, 0.4f, 1f);
            style.borderBottomColor = new Color(0.4f, 0.4f, 0.4f, 1f);
            style.borderLeftColor = new Color(0.4f, 0.4f, 0.4f, 1f);
            style.borderRightColor = new Color(0.4f, 0.4f, 0.4f, 1f);
            style.borderTopWidth = 1;
            style.borderBottomWidth = 1;
            style.borderLeftWidth = 1;
            style.borderRightWidth = 1;
            
            // Initially hidden
            style.display = DisplayStyle.None;
        }
        
        /// <summary>
        /// Inspect a specific node view
        /// </summary>
        public void InspectNodeView(StateNodeView nodeView)
        {
            _selectedNodeView = nodeView;

            if (nodeView == null)
            {
                style.display = DisplayStyle.None;
            }
            else
            {
                style.display = DisplayStyle.Flex;
                RefreshInspector();
            }
        }
        
        /// <summary>
        /// Refresh the inspector content
        /// </summary>
        private void RefreshInspector()
        {
            _contentContainer.Clear();
            
            if (_selectedNodeView == null)
            {
                ShowNoSelection();
                return;
            }
            
            _titleLabel.text = $"Inspector - {_selectedNodeView.StateNode.GetType().Name}";
            
            // Let the node view create its own inspector UI
            _selectedNodeView.OnInspectorGUI(_contentContainer);
        }
        
        /// <summary>
        /// Show message when no node is selected
        /// </summary>
        private void ShowNoSelection()
        {
            _contentContainer.Clear();
            var noSelectionLabel = new Label("No node selected");
            noSelectionLabel.style.color = Color.gray;
            noSelectionLabel.style.fontSize = 12;
            noSelectionLabel.style.alignSelf = Align.Center;
            noSelectionLabel.style.marginTop = 20;
            _contentContainer.Add(noSelectionLabel);
        }
        
        /// <summary>
        /// Hide the inspector
        /// </summary>
        public void Hide()
        {
            style.display = DisplayStyle.None;
            _selectedNodeView = null;
        }
        
        /// <summary>
        /// Show the inspector (if a node is selected)
        /// </summary>
        public void Show()
        {
            if (_selectedNodeView != null)
            {
                style.display = DisplayStyle.Flex;
            }
        }
    }
}
