using System;
using UnityEngine;
using UnityEditor;
using UnityEngine.UIElements;
using UnityEditor.UIElements;
using StateScript;

namespace StateScript.Editor
{
    /// <summary>
    /// Inspector view for selected nodes in the StateFlow editor
    /// </summary>
    public class StateFlowInspectorView : VisualElement
    {
        private StateNode _selectedNode;
        private VisualElement _contentContainer;
        private Label _titleLabel;
        
        public StateFlowInspectorView()
        {
            CreateUI();
        }
        
        private void CreateUI()
        {
            style.backgroundColor = new Color(0.25f, 0.25f, 0.25f, 1f);
            style.paddingTop = 5;
            style.paddingBottom = 5;
            style.paddingLeft = 5;
            style.paddingRight = 5;
            style.marginTop = 10;
            
            // Title
            _titleLabel = new Label("Inspector");
            _titleLabel.style.fontSize = 14;
            _titleLabel.style.unityFontStyleAndWeight = FontStyle.Bold;
            _titleLabel.style.marginBottom = 5;
            Add(_titleLabel);
            
            // Content container
            _contentContainer = new VisualElement();
            Add(_contentContainer);
            
            ShowNoSelection();
        }
        
        public void InspectNode(StateNode node)
        {
            _selectedNode = node;

            if (node == null)
            {
                // Hide inspector when no node is selected
                style.display = DisplayStyle.None;
            }
            else
            {
                // Show inspector when a node is selected
                style.display = DisplayStyle.Flex;
                RefreshInspector();
            }
        }
        
        private void RefreshInspector()
        {
            _contentContainer.Clear();
            
            if (_selectedNode == null)
            {
                ShowNoSelection();
                return;
            }
            
            _titleLabel.text = $"Inspector - {_selectedNode.GetType().Name}";
            
            // Basic properties
            CreateBasicProperties();
            
            // Node-specific properties
            CreateNodeSpecificProperties();
        }
        
        private void ShowNoSelection()
        {
            _titleLabel.text = "Inspector";
            _contentContainer.Clear();
            
            var noSelectionLabel = new Label("No node selected");
            noSelectionLabel.style.color = Color.gray;
            noSelectionLabel.style.unityTextAlign = TextAnchor.MiddleCenter;
            noSelectionLabel.style.marginTop = 20;
            _contentContainer.Add(noSelectionLabel);
        }
        
        private void CreateBasicProperties()
        {
            // Node ID
            var idField = new TextField("Node ID");
            idField.value = _selectedNode.Property.Id.ToString();
            idField.SetEnabled(false);
            _contentContainer.Add(idField);

            // Type
            var typeField = new TextField("Type");
            typeField.value = _selectedNode.GetType().Name;
            typeField.SetEnabled(false);
            _contentContainer.Add(typeField);
            
            // Name
            var nameField = new TextField("Name");
            nameField.value = _selectedNode.EditorProperty.Name.ToString();
            nameField.RegisterValueChangedCallback(evt =>
            {
                _selectedNode.EditorProperty.Name = new Fixed32String(evt.newValue);
            });
            _contentContainer.Add(nameField);
            
            // Description
            var descriptionField = new TextField("Description");
            descriptionField.value = _selectedNode.EditorProperty.Description.ToString();
            descriptionField.multiline = true;
            descriptionField.style.height = 60;
            descriptionField.RegisterValueChangedCallback(evt =>
            {
                _selectedNode.EditorProperty.Description = new Fixed32String(evt.newValue);
            });
            _contentContainer.Add(descriptionField);
            
            // Color
            var colorField = new ColorField("Color");
            colorField.value = _selectedNode.EditorProperty.NodeColor;
            colorField.RegisterValueChangedCallback(evt =>
            {
                _selectedNode.EditorProperty.NodeColor = evt.newValue;
            });
            _contentContainer.Add(colorField);
            
            // Position
            var positionField = new Vector2Field("Position");
            positionField.value = _selectedNode.EditorProperty.Position;
            positionField.SetEnabled(false);
            _contentContainer.Add(positionField);
            
            // Status
            var statusField = new TextField("Status");
            statusField.value = _selectedNode.Status.ToString();
            statusField.SetEnabled(false);
            _contentContainer.Add(statusField);
            
            // Separator
            var separator = new VisualElement();
            separator.style.height = 1;
            separator.style.backgroundColor = Color.gray;
            separator.style.marginTop = 10;
            separator.style.marginBottom = 10;
            _contentContainer.Add(separator);
        }
        
        private void CreateNodeSpecificProperties()
        {
            switch (_selectedNode)
            {
                case StateActionNode actionNode:
                    CreateActionNodeProperties(actionNode);
                    break;
                case StateConditionNode conditionNode:
                    CreateConditionNodeProperties(conditionNode);
                    break;
                case StateListenerNode listenerNode:
                    CreateListenerNodeProperties(listenerNode);
                    break;
            }
        }
        
        private void CreateActionNodeProperties(StateActionNode actionNode)
        {
            var label = new Label("Actions");
            label.style.fontSize = 12;
            label.style.unityFontStyleAndWeight = FontStyle.Bold;
            label.style.marginBottom = 5;
            _contentContainer.Add(label);

            // Timeline view for actions
            var timelineView = new TimelineActionView();
            timelineView.SetActions(actionNode.Actions);
            timelineView.style.marginBottom = 10;

            // Handle action selection for detailed editing
            timelineView.OnActionSelected += (index) => {
                CreateSelectedActionDetails(actionNode.Actions[index], index, actionNode, timelineView);
            };

            _contentContainer.Add(timelineView);

            // Add action button
            var addButton = new Button(() => ShowAddActionMenu(actionNode, timelineView));
            addButton.text = "Add Action";
            addButton.style.marginTop = 5;
            _contentContainer.Add(addButton);
        }
        

        
        private void CreateSelectedActionDetails(StateAction action, int index, StateActionNode actionNode, TimelineActionView timelineView)
        {
            // Remove any existing action details
            var existingDetails = _contentContainer.Q("action-details");
            if (existingDetails != null)
            {
                _contentContainer.Remove(existingDetails);
            }

            // Create action details container
            var detailsContainer = new VisualElement();
            detailsContainer.name = "action-details";
            detailsContainer.style.backgroundColor = new Color(0.25f, 0.25f, 0.25f, 1f);
            detailsContainer.style.marginTop = 5;
            detailsContainer.style.marginBottom = 5;
            detailsContainer.style.paddingTop = 10;
            detailsContainer.style.paddingBottom = 10;
            detailsContainer.style.paddingLeft = 10;
            detailsContainer.style.paddingRight = 10;
            detailsContainer.style.borderTopLeftRadius = 4;
            detailsContainer.style.borderTopRightRadius = 4;
            detailsContainer.style.borderBottomLeftRadius = 4;
            detailsContainer.style.borderBottomRightRadius = 4;

            // Header with action type and delete button
            var header = new VisualElement();
            header.style.flexDirection = FlexDirection.Row;
            header.style.justifyContent = Justify.SpaceBetween;
            header.style.marginBottom = 10;

            var titleLabel = new Label($"Edit {action.GetType().Name}");
            titleLabel.style.fontSize = 12;
            titleLabel.style.unityFontStyleAndWeight = FontStyle.Bold;

            var deleteButton = new Button(() => {
                actionNode.Actions.RemoveAt(index);
                timelineView.SetActions(actionNode.Actions);
                _contentContainer.Remove(detailsContainer);
            });
            deleteButton.text = "x";
            deleteButton.style.width = 10;
            deleteButton.style.height = 10;
            deleteButton.style.fontSize = 12;
            deleteButton.style.backgroundColor = new Color(0.8f, 0.3f, 0.3f, 1f);

            header.Add(titleLabel);
            header.Add(deleteButton);
            detailsContainer.Add(header);

            // Duration field
            var durationField = new FloatField("Duration");
            durationField.value = action.Duration;
            durationField.RegisterValueChangedCallback(evt => {
                // Ensure duration is never negative
                var newDuration = Mathf.Max(0f, evt.newValue);
                action.Duration = newDuration;

                // Update field if value was clamped
                if (newDuration != evt.newValue)
                {
                    durationField.SetValueWithoutNotify(newDuration);
                }

                timelineView.SetActions(actionNode.Actions); // Refresh timeline
            });
            detailsContainer.Add(durationField);

            // Delay field
            var delayField = new FloatField("Delay");
            delayField.value = action.Delay;
            delayField.RegisterValueChangedCallback(evt => {
                // Ensure delay is never negative
                var newDelay = Mathf.Max(0f, evt.newValue);
                action.Delay = newDelay;

                // Update field if value was clamped
                if (newDelay != evt.newValue)
                {
                    delayField.SetValueWithoutNotify(newDelay);
                }

                timelineView.SetActions(actionNode.Actions); // Refresh timeline
            });
            detailsContainer.Add(delayField);

            // Action-specific properties
            if (action is LogAction logAction)
            {
                var messageField = new TextField("Message");
                messageField.value = logAction.Message;
                messageField.RegisterValueChangedCallback(evt => logAction.Message = evt.newValue);
                detailsContainer.Add(messageField);
            }

            _contentContainer.Add(detailsContainer);
        }

        private void ShowAddActionMenu(StateActionNode actionNode, TimelineActionView timelineView)
        {
            var menu = new GenericMenu();

            menu.AddItem(new GUIContent("Wait Action"), false, () => {
                actionNode.Actions.Add(new WaitAction(1.0f));
                timelineView.SetActions(actionNode.Actions);
            });

            menu.AddItem(new GUIContent("Log Action"), false, () => {
                actionNode.Actions.Add(new LogAction("Hello World"));
                timelineView.SetActions(actionNode.Actions);
            });

            menu.ShowAsContext();
        }
        
        private void CreateConditionNodeProperties(StateConditionNode conditionNode)
        {
            var label = new Label("Condition");
            label.style.fontSize = 12;
            label.style.unityFontStyleAndWeight = FontStyle.Bold;
            label.style.marginBottom = 5;
            _contentContainer.Add(label);
            
            // Current condition
            var currentCondition = conditionNode.Condition?.GetType().Name ?? "None";
            var conditionField = new TextField("Current Condition");
            conditionField.value = currentCondition;
            conditionField.SetEnabled(false);
            _contentContainer.Add(conditionField);
            
            // Set condition buttons
            var alwaysTrueButton = new Button(() => {
                conditionNode.SetSimpleCondition(new AlwaysTrueCondition());
                RefreshInspector();
            });
            alwaysTrueButton.text = "Set Always True";
            _contentContainer.Add(alwaysTrueButton);
            
            var alwaysFalseButton = new Button(() => {
                conditionNode.SetSimpleCondition(new AlwaysFalseCondition());
                RefreshInspector();
            });
            alwaysFalseButton.text = "Set Always False";
            _contentContainer.Add(alwaysFalseButton);
        }
        
        private void CreateListenerNodeProperties(StateListenerNode listenerNode)
        {
            var label = new Label("Event Listener");
            label.style.fontSize = 12;
            label.style.unityFontStyleAndWeight = FontStyle.Bold;
            label.style.marginBottom = 5;
            _contentContainer.Add(label);
            
            // Event name
            var eventField = new TextField("Event Name");
            eventField.value = listenerNode.EventType.Name.ToString();
            eventField.RegisterValueChangedCallback(evt =>
            {
                listenerNode.EventType = new EventType(evt.newValue);
            });
            _contentContainer.Add(eventField);
        }
    }
}
