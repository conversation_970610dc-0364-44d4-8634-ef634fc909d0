using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEditor;
using UnityEngine.UIElements;
using UnityEditor.UIElements;
using UnityEditor.Experimental.GraphView;
using StateScript;

namespace StateScript.Editor
{
    /// <summary>
    /// Base visual representation of a StateNode in the GraphView
    /// </summary>
    public abstract class StateNodeView : Node
    {
        public event Action<StateNode> OnNodeSelected;
        
        public StateNode StateNode { get; private set; }
        
        private Dictionary<UniqueId, StatePortView> _portViews = new Dictionary<UniqueId, StatePortView>();
        
        protected StateNodeView(StateNode stateNode)
        {
            StateNode = stateNode;

            title = stateNode.EditorProperty.Name.ToString();

            // Add CSS classes for styling
            AddToClassList("node");
            AddNodeTypeClass();

            // Set node styling
            SetNodeStyling();

            // Create node-specific content
            CreateNodeContent();

            // Create ports
            CreatePorts();

            // Handle selection
            RegisterCallback<MouseDownEvent>(OnMouseDown);

            // Optional: Add custom selection behavior
            RegisterCallback<MouseEnterEvent>(OnMouseEnter);
            RegisterCallback<MouseLeaveEvent>(OnMouseLeave);

            // Make node movable and selectable (removed Resizable to hide resize handle)
            capabilities |= Capabilities.Movable | Capabilities.Selectable;
        }

        /// <summary>
        /// Factory method to create the appropriate node view for a given StateNode
        /// </summary>
        public static StateNodeView Create(StateNode stateNode)
        {
            return stateNode switch
            {
                StateActionNode actionNode => new StateActionNodeView(actionNode),
                StateConditionNode conditionNode => new StateConditionNodeView(conditionNode),
                StateListenerNode listenerNode => new StateListenerNodeView(listenerNode),
                _ => throw new System.ArgumentException($"Unknown node type: {stateNode.GetType()}")
            };
        }

        /// <summary>
        /// Override this method to create node-specific visual content
        /// </summary>
        protected virtual void CreateNodeContent()
        {
            // Base implementation does nothing
        }
        
        private void SetNodeStyling()
        {
            var color = StateNode.EditorProperty.NodeColor;

            // Set default colors based on node type if not set
            if (color == Color.white)
            {
                color = StateNode switch
                {
                    StateActionNode => new Color(0.2f, 0.6f, 0.2f, 1f), // Green
                    StateConditionNode => new Color(0.6f, 0.6f, 0.2f, 1f), // Yellow
                    StateListenerNode => new Color(0.2f, 0.2f, 0.6f, 1f), // Blue
                    _ => new Color(0.4f, 0.4f, 0.4f, 1f) // Gray
                };
                StateNode.EditorProperty.NodeColor = color;
            }

            // Apply proper styling with rounded corners
            var titleContainer = this.Q("title");
            if (titleContainer != null)
            {
                titleContainer.style.backgroundColor = color;
                titleContainer.style.borderTopLeftRadius = 6;
                titleContainer.style.borderTopRightRadius = 6;
                titleContainer.style.color = Color.white;
                titleContainer.style.unityFontStyleAndWeight = FontStyle.Bold;
                titleContainer.style.fontSize = 12;
                titleContainer.style.paddingTop = 4;
                titleContainer.style.paddingBottom = 4;
                titleContainer.style.paddingLeft = 8;
                titleContainer.style.paddingRight = 8;
            }

            // Style the main container
            var mainContainer = this.Q("contents");
            if (mainContainer != null)
            {
                mainContainer.style.backgroundColor = new Color(0.2f, 0.2f, 0.2f, 0.9f);
                mainContainer.style.borderBottomLeftRadius = 6;
                mainContainer.style.borderBottomRightRadius = 6;
                mainContainer.style.borderTopWidth = 0;
                mainContainer.style.borderBottomWidth = 1;
                mainContainer.style.borderLeftWidth = 1;
                mainContainer.style.borderRightWidth = 1;
                mainContainer.style.borderBottomColor = color;
                mainContainer.style.borderLeftColor = color;
                mainContainer.style.borderRightColor = color;
            }

            // Ensure the node title is visible and properly styled
            var titleLabel = this.Q<Label>("title-label");
            if (titleLabel != null)
            {
                titleLabel.text = StateNode.EditorProperty.Name.ToString();
                titleLabel.style.color = Color.white;
                titleLabel.style.unityFontStyleAndWeight = FontStyle.Bold;
            }
        }

        private void AddNodeTypeClass()
        {
            // Add CSS class based on node type for styling
            switch (StateNode)
            {
                case StateActionNode:
                    AddToClassList("action-node");
                    break;
                case StateConditionNode:
                    AddToClassList("condition-node");
                    break;
                case StateListenerNode:
                    AddToClassList("listener-node");
                    break;
                default:
                    AddToClassList("custom-node");
                    break;
            }
        }

        protected virtual void CreatePorts()
        {
            Debug.Log("Creating ports : " + StateNode.Property.Ports.Count);

            foreach (var statePort in StateNode.Property.Ports)
            {
                var portView = CreatePortView(statePort);
                _portViews[statePort.Id] = portView;

                if (statePort.Type == PortType.Input)
                {
                    inputContainer.Add(portView);
                }
                else
                {
                    outputContainer.Add(portView);
                }

                Debug.Log($"Created port: {portView.portName}, Direction: {portView.direction}, PickingMode: {portView.pickingMode}");
            }

            // Force a layout update to ensure ports are properly positioned
            schedule.Execute(() => {
                RefreshPorts();
            }).ExecuteLater(1);
        }
        
        private StatePortView CreatePortView(StatePort statePort)
        {
            var direction = statePort.Type == PortType.Input ? Direction.Input : Direction.Output;
            var capacity = Port.Capacity.Multi; // Allow multiple connections

            var portView = new StatePortView(statePort, direction, capacity);
            portView.portName = statePort.Name.ToString();

            // Ensure the port is properly configured for connections
            portView.pickingMode = PickingMode.Position;

            return portView;
        }
        
        public StatePortView GetPortView(UniqueId portId)
        {
            return _portViews.TryGetValue(portId, out var portView) ? portView : null;
        }
        
        private void OnMouseDown(MouseDownEvent evt)
        {
            if (evt.clickCount == 1)
            {
                OnNodeSelected?.Invoke(StateNode);
            }
        }

        private void OnMouseEnter(MouseEnterEvent evt)
        {
            
        }

        private void OnMouseLeave(MouseLeaveEvent evt)
        {
            
        }
        
        public override void SetPosition(Rect newPos)
        {
            base.SetPosition(newPos);
            StateNode.EditorProperty.Position = newPos.position;
        }
        
        public virtual void UpdateTitle()
        {
            title = StateNode.EditorProperty.Name.ToString();

            // Also update the title label if it exists
            var titleLabel = this.Q<Label>("title-label");
            if (titleLabel != null)
            {
                titleLabel.text = StateNode.EditorProperty.Name.ToString();
            }
        }
        
        /// <summary>
        /// Override InstantiatePort to return our custom StatePortView
        /// </summary>
        public override Port InstantiatePort(Orientation orientation, Direction direction, Port.Capacity capacity, System.Type type)
        {
            var tempStatePort = new StatePort("Temp", direction == Direction.Input ? PortType.Input : PortType.Output, StateNode.Property.Id);
            return new StatePortView(tempStatePort, direction, capacity);
        }

        /// <summary>
        /// Refreshes the node's ports by recreating them from the StateNode data
        /// </summary>
        public void RefreshNodePorts()
        {
            // Clear existing ports
            inputContainer.Clear();
            outputContainer.Clear();
            _portViews.Clear();

            // Recreate ports
            CreatePorts();

            // Call base RefreshPorts to update layout
            base.RefreshPorts();
        }

        /// <summary>
        /// Override ToggleCollapse to handle Unity's built-in collapse/expand functionality
        /// This ensures ports are properly refreshed when the node's expanded state changes
        /// </summary>
        protected override void ToggleCollapse()
        {
            base.ToggleCollapse();

            // After Unity handles the expand/collapse, refresh our ports to ensure proper layout
            // Use a delayed call to ensure the layout has been updated
            schedule.Execute(() => RefreshNodePorts()).ExecuteLater(1);
        }

        /// <summary>
        /// Override this method to create custom inspector UI for this node type
        /// </summary>
        public virtual void OnInspectorGUI(VisualElement container)
        {
            // Default implementation - basic node properties
            CreateBasicInspectorProperties(container);
        }

        /// <summary>
        /// Creates the basic inspector properties that all nodes share
        /// </summary>
        protected virtual void CreateBasicInspectorProperties(VisualElement container)
        {
            // Node ID
            var idField = new TextField("Node ID");
            idField.value = StateNode.Property.Id.ToString();
            idField.SetEnabled(false);
            container.Add(idField);

            // Type
            var typeField = new TextField("Type");
            typeField.value = StateNode.GetType().Name;
            typeField.SetEnabled(false);
            container.Add(typeField);

            // Name
            var nameField = new TextField("Name");
            nameField.value = StateNode.EditorProperty.Name.ToString();
            nameField.RegisterValueChangedCallback(evt =>
            {
                StateNode.EditorProperty.Name = new Fixed32String(evt.newValue);
                // Update the node's title immediately
                UpdateTitle();
            });
            container.Add(nameField);

            // Description
            var descField = new TextField("Description");
            descField.value = StateNode.EditorProperty.Description.ToString();
            descField.RegisterValueChangedCallback(evt =>
            {
                StateNode.EditorProperty.Description = new Fixed32String(evt.newValue);
                // Description doesn't affect title, but we could add other updates here if needed
            });
            container.Add(descField);

            // Color
            var colorField = new ColorField("Color");
            colorField.value = StateNode.EditorProperty.NodeColor;
            colorField.RegisterValueChangedCallback(evt =>
            {
                StateNode.EditorProperty.NodeColor = evt.newValue;
                // Update the node's visual styling when color changes
                SetNodeStyling();
            });
            container.Add(colorField);

            // Position (read-only)
            var positionField = new Vector2Field("Position");
            positionField.value = StateNode.EditorProperty.Position;
            positionField.SetEnabled(false);
            container.Add(positionField);

            // Status (read-only)
            var statusField = new TextField("Status");
            statusField.value = StateNode.Status.ToString();
            statusField.SetEnabled(false);
            container.Add(statusField);
        }
    }
    
    /// <summary>
    /// Custom port view for StateScript ports
    /// </summary>
    public class StatePortView : Port
    {
        public StatePort StatePort { get; set; }

        // Use a consistent type for all ports to ensure connections work
        private static Type connectionType = typeof(object); // Use object type for broader compatibility

        public StatePortView(StatePort statePort, Direction direction, Capacity capacity)
            : base(Orientation.Horizontal, direction, capacity, connectionType)
        {
            StatePort = statePort;
            portName = statePort.Name.ToString();

            // Set port color based on type
            var color = direction == Direction.Input ? Color.cyan : Color.magenta;
            portColor = color;

            // Style the port
            this.style.minWidth = 30;
            this.style.minHeight = 24;

            // Make sure the port is visible and clickable
            EnableInClassList("port", true);

            // Ensure the port can be picked for dragging
            pickingMode = PickingMode.Position;

            // Schedule a delayed call to ensure the connector is properly set up
            schedule.Execute(() => {
                // Make sure the port connector (the circle) is also pickable
                var connector = this.Q("connector");
                if (connector != null)
                {
                    connector.pickingMode = PickingMode.Position;
                    Debug.Log($"Port connector found and made pickable for {portName}");
                }
                else
                {
                    Debug.LogWarning($"Port connector not found for {portName}");
                }
            }).ExecuteLater(1);

            // Add a highlight effect on hover
            this.RegisterCallback<MouseEnterEvent>(evt => {
                this.AddToClassList("port-highlight");
            });

            this.RegisterCallback<MouseLeaveEvent>(evt => {
                this.RemoveFromClassList("port-highlight");
            });
        }


        public override void OnStartEdgeDragging()
        {
            base.OnStartEdgeDragging();
            Debug.Log("-----Start Edge Dragging");
        }

        public override bool ContainsPoint(Vector2 localPoint)
        {
            // Expand the clickable area slightly
            var bounds = this.layout;
            var expandedRect = new Rect(
                bounds.x - 10,
                bounds.y - 10,
                bounds.width + 20,
                bounds.height + 20
            );
            return expandedRect.Contains(localPoint);
        }
    }
    
    /// <summary>
    /// Popup window for editing node properties
    /// </summary>
    public class NodeEditorPopup : PopupWindowContent
    {
        private StateNode _node;
        private Vector2 _scrollPosition;
        
        public NodeEditorPopup(StateNode node)
        {
            _node = node;
        }
        
        public override Vector2 GetWindowSize()
        {
            return new Vector2(300, 400);
        }
        
        public override void OnGUI(Rect rect)
        {
            GUILayout.Label("Node Properties", EditorStyles.boldLabel);
            
            _scrollPosition = EditorGUILayout.BeginScrollView(_scrollPosition);
            
            // Basic properties
            EditorGUI.BeginChangeCheck();
            
            var newName = EditorGUILayout.TextField("Name", _node.EditorProperty.Name.ToString());
            if (EditorGUI.EndChangeCheck())
            {
                _node.EditorProperty.Name = new Fixed32String(newName);
            }
            
            EditorGUI.BeginChangeCheck();
            var newDescription = EditorGUILayout.TextField("Description", _node.EditorProperty.Description.ToString());
            if (EditorGUI.EndChangeCheck())
            {
                _node.EditorProperty.Description = new Fixed32String(newDescription);
            }
            
            EditorGUI.BeginChangeCheck();
            var newColor = EditorGUILayout.ColorField("Color", _node.EditorProperty.NodeColor);
            if (EditorGUI.EndChangeCheck())
            {
                _node.EditorProperty.NodeColor = newColor;
            }
            
            EditorGUILayout.Space();
            
            // Node-specific properties
            DrawNodeSpecificProperties();
            
            EditorGUILayout.EndScrollView();
            
            EditorGUILayout.Space();
            
            if (GUILayout.Button("Close"))
            {
                editorWindow.Close();
            }
        }
        
        private void DrawNodeSpecificProperties()
        {
            switch (_node)
            {
                case StateActionNode actionNode:
                    DrawActionNodeProperties(actionNode);
                    break;
                case StateConditionNode conditionNode:
                    DrawConditionNodeProperties(conditionNode);
                    break;
                case StateListenerNode listenerNode:
                    DrawListenerNodeProperties(listenerNode);
                    break;
            }
        }
        
        private void DrawActionNodeProperties(StateActionNode actionNode)
        {
            GUILayout.Label("Actions", EditorStyles.boldLabel);
            
            for (int i = 0; i < actionNode.Actions.Count; i++)
            {
                var action = actionNode.Actions[i];
                EditorGUILayout.LabelField($"Action {i}: {action.GetType().Name}");
                
                EditorGUI.indentLevel++;
                action.Duration = EditorGUILayout.FloatField("Duration", action.Duration);
                action.Delay = EditorGUILayout.FloatField("Delay", action.Delay);
                EditorGUI.indentLevel--;
                
                if (GUILayout.Button("Remove"))
                {
                    actionNode.Actions.RemoveAt(i);
                    break;
                }
                
                EditorGUILayout.Space();
            }
            
            if (GUILayout.Button("Add Wait Action"))
            {
                actionNode.Actions.Add(new WaitAction(1.0f));
            }
            
            if (GUILayout.Button("Add Log Action"))
            {
                actionNode.Actions.Add(new LogAction("Hello World"));
            }
        }
        
        private void DrawConditionNodeProperties(StateConditionNode conditionNode)
        {
            GUILayout.Label("Condition", EditorStyles.boldLabel);
            
            if (conditionNode.Condition != null)
            {
                EditorGUILayout.LabelField($"Type: {conditionNode.Condition.GetType().Name}");
            }
            else
            {
                EditorGUILayout.LabelField("No condition set");
            }
            
            if (GUILayout.Button("Set Always True"))
            {
                conditionNode.SetSimpleCondition(new AlwaysTrueCondition());
            }
            
            if (GUILayout.Button("Set Always False"))
            {
                conditionNode.SetSimpleCondition(new AlwaysFalseCondition());
            }
        }
        
        private void DrawListenerNodeProperties(StateListenerNode listenerNode)
        {
            GUILayout.Label("Event Listener", EditorStyles.boldLabel);
            
            var eventName = EditorGUILayout.TextField("Event Name", listenerNode.EventType.Name.ToString());
            if (eventName != listenerNode.EventType.Name.ToString())
            {
                listenerNode.EventType = new EventType(eventName);
            }
        }
    }

    /// <summary>
    /// Node view for StateActionNode - displays actions in a timeline format
    /// </summary>
    public class StateActionNodeView : StateNodeView
    {
        private TimelineActionView _timelineView;

        public StateActionNodeView(StateActionNode actionNode) : base(actionNode)
        {
        }

        public StateActionNode ActionNode => StateNode as StateActionNode;

        protected override void CreateNodeContent()
        {
            base.CreateNodeContent();

            // Create timeline view for actions
            _timelineView = new TimelineActionView();
            _timelineView.style.marginTop = 5;
            _timelineView.style.marginBottom = 5;
            _timelineView.style.marginLeft = 5;
            _timelineView.style.marginRight = 5;

            // Make timeline blend with the node's content area
            _timelineView.style.backgroundColor = StyleKeyword.Null; // Inherit from parent

            // Set initial actions
            _timelineView.SetActions(ActionNode.Actions);

            // Handle action selection
            _timelineView.OnActionSelected += OnActionSelected;

            // Find the contents container (the dark area inside the green border)
            var contentsContainer = this.Q("contents");
            if (contentsContainer != null)
            {
                // Add timeline to the contents container (inside the green border)
                contentsContainer.Add(_timelineView);
            }
            else
            {
                // Fallback: add to mainContainer if contents not found
                mainContainer.Add(_timelineView);
            }

            // Make the node wider to accommodate timeline
            style.minWidth = 300;
        }

        private void OnActionSelected(int actionIndex)
        {
            // Could trigger inspector selection or other UI updates
            // For now, just log the selection
            UnityEngine.Debug.Log($"Action {actionIndex} selected in node {StateNode.EditorProperty.Name}");
        }

        /// <summary>
        /// Refresh the timeline when actions change
        /// </summary>
        public void RefreshTimeline()
        {
            _timelineView?.SetActions(ActionNode.Actions);
        }

        /// <summary>
        /// Update the node when the underlying StateNode changes
        /// </summary>
        public override void UpdateTitle()
        {
            base.UpdateTitle();
            RefreshTimeline();
        }

        /// <summary>
        /// Create custom inspector UI for StateActionNode
        /// </summary>
        public override void OnInspectorGUI(VisualElement container)
        {
            // Call base implementation for common properties
            base.OnInspectorGUI(container);

            // Add separator
            var separator = new VisualElement();
            separator.style.height = 1;
            separator.style.backgroundColor = Color.gray;
            separator.style.marginTop = 10;
            separator.style.marginBottom = 10;
            container.Add(separator);

            // Actions section
            var actionsLabel = new Label("Actions");
            actionsLabel.style.fontSize = 12;
            actionsLabel.style.unityFontStyleAndWeight = FontStyle.Bold;
            actionsLabel.style.marginBottom = 5;
            container.Add(actionsLabel);

            // Timeline view for actions
            var timelineView = new TimelineActionView();
            timelineView.SetActions(ActionNode.Actions);
            timelineView.style.marginBottom = 10;

            // Handle action selection for detailed editing
            timelineView.OnActionSelected += (index) => {
                CreateSelectedActionDetails(ActionNode.Actions[index], index, ActionNode, timelineView, container);
            };

            container.Add(timelineView);
        }

        /// <summary>
        /// Create detailed UI for selected action
        /// </summary>
        private void CreateSelectedActionDetails(StateAction action, int index, StateActionNode actionNode, TimelineActionView timelineView, VisualElement container)
        {
            // Remove any existing action details
            var existingDetails = container.Q("action-details");
            existingDetails?.RemoveFromHierarchy();

            // Create details container
            var detailsContainer = new VisualElement();
            detailsContainer.name = "action-details";
            detailsContainer.style.marginTop = 10;
            detailsContainer.style.paddingTop = 10;
            detailsContainer.style.paddingBottom = 10;
            detailsContainer.style.paddingLeft = 10;
            detailsContainer.style.paddingRight = 10;
            detailsContainer.style.backgroundColor = new Color(0.2f, 0.2f, 0.2f, 1f);
            detailsContainer.style.borderTopLeftRadius = 4;
            detailsContainer.style.borderTopRightRadius = 4;
            detailsContainer.style.borderBottomLeftRadius = 4;
            detailsContainer.style.borderBottomRightRadius = 4;

            // Action details header
            var headerContainer = new VisualElement();
            headerContainer.style.flexDirection = FlexDirection.Row;
            headerContainer.style.justifyContent = Justify.SpaceBetween;
            headerContainer.style.alignItems = Align.Center;
            headerContainer.style.marginBottom = 10;

            var actionLabel = new Label($"Action {index}: {action.GetType().Name}");
            actionLabel.style.fontSize = 12;
            actionLabel.style.unityFontStyleAndWeight = FontStyle.Bold;
            headerContainer.Add(actionLabel);

            // Delete button
            var deleteButton = new Button(() => {
                actionNode.Actions.RemoveAt(index);
                timelineView.SetActions(actionNode.Actions);
                detailsContainer.RemoveFromHierarchy();
            });
            deleteButton.text = "x";
            deleteButton.style.width = 20;
            deleteButton.style.height = 20;
            deleteButton.style.fontSize = 12;
            headerContainer.Add(deleteButton);

            detailsContainer.Add(headerContainer);

            // Duration field
            var durationField = new FloatField("Duration");
            durationField.value = action.Duration;
            durationField.RegisterValueChangedCallback(evt => {
                // Ensure duration is never negative
                var newDuration = Mathf.Max(0f, evt.newValue);
                action.Duration = newDuration;

                // Update field if value was clamped
                if (newDuration != evt.newValue)
                {
                    durationField.SetValueWithoutNotify(newDuration);
                }

                timelineView.SetActions(actionNode.Actions); // Refresh timeline
            });
            detailsContainer.Add(durationField);

            // Delay field
            var delayField = new FloatField("Delay");
            delayField.value = action.Delay;
            delayField.RegisterValueChangedCallback(evt => {
                // Ensure delay is never negative
                var newDelay = Mathf.Max(0f, evt.newValue);
                action.Delay = newDelay;

                // Update field if value was clamped
                if (newDelay != evt.newValue)
                {
                    delayField.SetValueWithoutNotify(newDelay);
                }

                timelineView.SetActions(actionNode.Actions); // Refresh timeline
            });
            detailsContainer.Add(delayField);

            container.Add(detailsContainer);
        }
    }

    /// <summary>
    /// Node view for StateConditionNode
    /// </summary>
    public class StateConditionNodeView : StateNodeView
    {
        public StateConditionNodeView(StateConditionNode conditionNode) : base(conditionNode)
        {
        }

        public StateConditionNode ConditionNode => StateNode as StateConditionNode;

        protected override void CreateNodeContent()
        {
            base.CreateNodeContent();

            // Add condition-specific content if needed
        }

        /// <summary>
        /// Create custom inspector UI for StateConditionNode
        /// </summary>
        public override void OnInspectorGUI(VisualElement container)
        {
            // Call base implementation for common properties
            base.OnInspectorGUI(container);

            // Add separator
            var separator = new VisualElement();
            separator.style.height = 1;
            separator.style.backgroundColor = Color.gray;
            separator.style.marginTop = 10;
            separator.style.marginBottom = 10;
            container.Add(separator);

            // Condition section
            var conditionLabel = new Label("Condition");
            conditionLabel.style.fontSize = 12;
            conditionLabel.style.unityFontStyleAndWeight = FontStyle.Bold;
            conditionLabel.style.marginBottom = 5;
            container.Add(conditionLabel);

            // Current condition
            var currentCondition = ConditionNode.Condition?.GetType().Name ?? "None";
            var conditionField = new TextField("Current Condition");
            conditionField.value = currentCondition;
            conditionField.SetEnabled(false);
            container.Add(conditionField);

            // Set condition buttons
            var buttonContainer = new VisualElement();
            buttonContainer.style.flexDirection = FlexDirection.Row;
            buttonContainer.style.marginTop = 5;

            var alwaysTrueButton = new Button(() => {
                ConditionNode.SetSimpleCondition(new AlwaysTrueCondition());
                // Refresh the inspector to show updated condition
                OnInspectorGUI(container);
            });
            alwaysTrueButton.text = "Always True";
            buttonContainer.Add(alwaysTrueButton);

            var alwaysFalseButton = new Button(() => {
                ConditionNode.SetSimpleCondition(new AlwaysFalseCondition());
                // Refresh the inspector to show updated condition
                OnInspectorGUI(container);
            });
            alwaysFalseButton.text = "Always False";
            buttonContainer.Add(alwaysFalseButton);

            container.Add(buttonContainer);
        }
    }

    /// <summary>
    /// Node view for StateListenerNode
    /// </summary>
    public class StateListenerNodeView : StateNodeView
    {
        public StateListenerNodeView(StateListenerNode listenerNode) : base(listenerNode)
        {
        }

        public StateListenerNode ListenerNode => StateNode as StateListenerNode;

        protected override void CreateNodeContent()
        {
            base.CreateNodeContent();

            // Add listener-specific content if needed
        }

        protected override void CreatePorts()
        {
            base.CreatePorts();
            
            inputContainer.Clear();
            inputContainer.style.display = DisplayStyle.None;
        }

        /// <summary>
        /// Create custom inspector UI for StateListenerNode
        /// </summary>
        public override void OnInspectorGUI(VisualElement container)
        {
            // Call base implementation for common properties
            base.OnInspectorGUI(container);

            // Add separator
            var separator = new VisualElement();
            separator.style.height = 1;
            separator.style.backgroundColor = Color.gray;
            separator.style.marginTop = 10;
            separator.style.marginBottom = 10;
            container.Add(separator);

            // Event Listener section
            var eventLabel = new Label("Event Listener");
            eventLabel.style.fontSize = 12;
            eventLabel.style.unityFontStyleAndWeight = FontStyle.Bold;
            eventLabel.style.marginBottom = 5;
            container.Add(eventLabel);

            // Event name field
            var eventField = new TextField("Event Name");
            eventField.value = ListenerNode.EventType.Name.ToString();
            eventField.RegisterValueChangedCallback(evt =>
            {
                ListenerNode.EventType = new EventType(evt.newValue);
            });
            container.Add(eventField);
        }
    }
}
