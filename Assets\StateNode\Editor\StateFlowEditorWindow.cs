using System;
using System.Linq;
using UnityEngine;
using UnityEngine.UIElements;
using UnityEditor;
using UnityEditor.UIElements;

namespace StateScript.Editor
{
    /// <summary>
    /// Main editor window for editing StateFlow graphs using Unity's GraphView
    /// </summary>
    public class StateFlowEditorWindow : EditorWindow
    {
        private string _currentAssetPath;
        private StateFlowAsset _currentAsset;
        private StateFlowGraphView _graphView;
        private NodeInspectorView _inspectorView;
        private StateFlowVariableView _variableView;
        private Label _fileNameLabel;
        private bool _isDirty = false;
        private ToolbarToggle _gridToggleButton;
        private VisualElement _welcomeScreen;

        [MenuItem("Window/StateScript/StateFlow Editor")]
        public static void OpenWindow()
        {
            var window = GetWindow<StateFlowEditorWindow>();
            window.titleContent = new GUIContent("StateFlow Editor");
            window.Show();
        }

        public static void OpenWindow(StateFlowAsset asset)
        {
            var window = GetWindow<StateFlowEditorWindow>();
            window.titleContent = new GUIContent($"StateFlow Editor - {asset.AssetName}");
            window.LoadAsset(asset);
            window.Show();
        }

        private void CreateGUI()
        {
            // Load stylesheet
            var styleSheet = AssetDatabase.LoadAssetAtPath<StyleSheet>("Assets/StateNode/Editor/StateFlowEditor.uss");
            if (styleSheet != null)
            {
                rootVisualElement.styleSheets.Add(styleSheet);
            }

            // Create the main container
            var root = rootVisualElement;
            CreateGUIManually(root);
            InitializeViews();

            // Register keyboard shortcuts
            RegisterKeyboardShortcuts();
        }

        private void CreateGUIManually(VisualElement root)
        {
            // Create toolbar
            var toolbar = new Toolbar();
            toolbar.style.justifyContent = Justify.SpaceBetween;

            // Left side of toolbar - File operations
            var leftToolbarContainer = new VisualElement();
            leftToolbarContainer.style.flexDirection = FlexDirection.Row;

            var newButton = new ToolbarButton(CreateNewAsset) { text = "New" };
            var loadButton = new ToolbarButton(LoadAssetDialog) { text = "Load" };
            var saveButton = new ToolbarButton(SaveAsset) { text = "Save" };

            leftToolbarContainer.Add(newButton);
            leftToolbarContainer.Add(loadButton);
            leftToolbarContainer.Add(saveButton);

            // Right side of toolbar - Grid toggle
            var rightToolbarContainer = new VisualElement();
            rightToolbarContainer.style.flexDirection = FlexDirection.Row;

            _gridToggleButton = new ToolbarToggle() { text = "Grid" };
            _gridToggleButton.value = true; // Default to on
            _gridToggleButton.RegisterValueChangedCallback(evt => ToggleGrid());
            rightToolbarContainer.Add(_gridToggleButton);

            toolbar.Add(leftToolbarContainer);
            toolbar.Add(rightToolbarContainer);

            // Create main layout
            var mainContainer = new VisualElement();
            mainContainer.style.flexDirection = FlexDirection.Row;
            mainContainer.style.flexGrow = 1;

            // Left panel for variables
            var leftPanel = new VisualElement();
            leftPanel.style.width = 300;
            leftPanel.style.backgroundColor = new Color(0.2f, 0.2f, 0.2f, 1f);
            leftPanel.name = "left-panel";

            // Graph view container
            var graphContainer = new VisualElement();
            graphContainer.style.flexGrow = 1;
            graphContainer.name = "graph-container";

            root.Add(toolbar);
            root.Add(mainContainer);
            mainContainer.Add(leftPanel);
            mainContainer.Add(graphContainer);
        }

        // Keep only the window menu item
        [MenuItem("Window/StateScript/StateFlow Editor", false, 1)]
        public static void OpenWindowMenuItem()
        {
            OpenWindow();
        }

        private void InitializeViews()
        {
            var root = rootVisualElement;

            // Find containers
            var leftPanel = root.Q("left-panel");
            var graphContainer = root.Q("graph-container");

            if (leftPanel == null || graphContainer == null)
            {
                Debug.LogError("Could not find required UI containers");
                return;
            }

            // Create graph view
            _graphView = new StateFlowGraphView(this);
            _graphView.StretchToParentSize();
            graphContainer.Add(_graphView);

            // Create welcome screen
            CreateWelcomeScreen(graphContainer);

            // Create file name label overlay
            _fileNameLabel = new Label("No file loaded");
            _fileNameLabel.style.position = Position.Absolute;
            _fileNameLabel.style.top = 10;
            _fileNameLabel.style.left = 10;
            _fileNameLabel.style.backgroundColor = new Color(0, 0, 0, 0.7f);
            _fileNameLabel.style.color = Color.white;
            _fileNameLabel.style.paddingTop = 4;
            _fileNameLabel.style.paddingBottom = 4;
            _fileNameLabel.style.paddingLeft = 8;
            _fileNameLabel.style.paddingRight = 8;
            _fileNameLabel.style.borderTopLeftRadius = 4;
            _fileNameLabel.style.borderTopRightRadius = 4;
            _fileNameLabel.style.borderBottomLeftRadius = 4;
            _fileNameLabel.style.borderBottomRightRadius = 4;
            _fileNameLabel.style.fontSize = 12;
            graphContainer.Add(_fileNameLabel);

            // Create variable view
            _variableView = new StateFlowVariableView();
            leftPanel.Add(_variableView);

            // Create inspector view as floating overlay on the right side
            _inspectorView = new NodeInspectorView();
            graphContainer.Add(_inspectorView);

            // Connect events
            _graphView.OnNodeViewSelected += _inspectorView.InspectNodeView;
            _variableView.OnVariableChanged += OnVariableChanged;

            // Initialize grid toggle button state
            UpdateGridToggleButton();

            // Show welcome screen initially (no asset loaded)
            ShowWelcomeScreen(true);
        }

        private void CreateWelcomeScreen(VisualElement parent)
        {
            _welcomeScreen = new VisualElement();
            _welcomeScreen.name = "welcome-screen";
            _welcomeScreen.style.position = Position.Absolute;
            _welcomeScreen.style.left = 0;
            _welcomeScreen.style.top = 0;
            _welcomeScreen.style.right = 0;
            _welcomeScreen.style.bottom = 0;
            _welcomeScreen.style.backgroundColor = new Color(0.15f, 0.15f, 0.15f, 1f);
            _welcomeScreen.style.justifyContent = Justify.Center;
            _welcomeScreen.style.alignItems = Align.Center;

            // Main content container
            var contentContainer = new VisualElement();
            contentContainer.style.alignItems = Align.Center;
            contentContainer.style.justifyContent = Justify.Center;

            // Load and display the folder icon
            var folderIcon = LoadFolderIcon();
            if (folderIcon != null)
            {
                folderIcon.style.width = 64;
                folderIcon.style.height = 64;
                folderIcon.style.marginBottom = 20;
                contentContainer.Add(folderIcon);
            }

            // Welcome message
            var messageLabel = new Label("Click to select a StateFlow asset, Or Drag and drop here.");
            messageLabel.style.fontSize = 16;
            messageLabel.style.color = new Color(0.8f, 0.8f, 0.8f, 1f);
            messageLabel.style.alignSelf = Align.Center;
            messageLabel.style.whiteSpace = WhiteSpace.Normal;
            messageLabel.style.maxWidth = 400;
            contentContainer.Add(messageLabel);

            // Make the welcome screen clickable and support drag-and-drop
            _welcomeScreen.RegisterCallback<MouseDownEvent>(OnWelcomeScreenClick);
            _welcomeScreen.RegisterCallback<DragEnterEvent>(OnDragEnter);
            _welcomeScreen.RegisterCallback<DragLeaveEvent>(OnDragLeave);
            _welcomeScreen.RegisterCallback<DragUpdatedEvent>(OnDragUpdated);
            _welcomeScreen.RegisterCallback<DragPerformEvent>(OnDragPerform);

            _welcomeScreen.Add(contentContainer);
            parent.Add(_welcomeScreen);
        }

        private VisualElement LoadFolderIcon()
        {
            // Load the imported SVG texture (without .svg extension since it's now imported as Texture2D)
            var iconPath = "Assets/StateNode/Editor/Resources/folder_icon";
            var folderTexture = AssetDatabase.LoadAssetAtPath<Texture2D>(iconPath);

            if (folderTexture != null)
            {
                // Create an image element for the folder texture
                var iconElement = new VisualElement();
                iconElement.style.backgroundImage = new StyleBackground(folderTexture);
                iconElement.style.backgroundSize = new BackgroundSize(BackgroundSizeType.Contain);
                iconElement.style.backgroundRepeat = new BackgroundRepeat(Repeat.NoRepeat, Repeat.NoRepeat);
                iconElement.style.backgroundPositionX = new BackgroundPosition(BackgroundPositionKeyword.Center);
                iconElement.style.backgroundPositionY = new BackgroundPosition(BackgroundPositionKeyword.Center);
                return iconElement;
            }
            else
            {
                // Fallback: create a simple folder icon using Unicode
                var iconLabel = new Label("📁");
                iconLabel.style.fontSize = 48;
                iconLabel.style.alignSelf = Align.Center;
                iconLabel.style.color = new Color(0.4f, 0.6f, 0.8f, 1f);
                return iconLabel;
            }
        }

        private void OnWelcomeScreenClick(MouseDownEvent evt)
        {
            LoadAssetDialog();
        }

        public void LoadAsset(string path)
        {
            if (string.IsNullOrEmpty(path))
                return;

            _currentAssetPath = path;
            LoadAsset(StateFlowAsset.LoadFromFile(path));
        }

        private void LoadAsset(StateFlowAsset asset)
        {
            _currentAsset = asset;

            if (_graphView != null)
            {
                _graphView.LoadStateFlow(_currentAsset.StateFlow);
            }

            if (_variableView != null)
            {
                _variableView.LoadVariables(_currentAsset.StateFlow.Variables);
            }

            SetDirty(false);
            UpdateFileNameDisplay();

            // Hide welcome screen when asset is loaded
            ShowWelcomeScreen(false);
        }

        private void ShowWelcomeScreen(bool show)
        {
            if (_welcomeScreen != null)
            {
                _welcomeScreen.style.display = show ? DisplayStyle.Flex : DisplayStyle.None;
            }
        }

        private void LoadAssetDialog()
        {
            // Open file dialog to select StateFlow asset
            var path = EditorUtility.OpenFilePanel("Select StateFlow Asset", "Assets", "stateflow");
            if (!string.IsNullOrEmpty(path))
            {
                // Convert absolute path to relative path
                var relativePath = FileUtil.GetProjectRelativePath(path);
                if (!string.IsNullOrEmpty(relativePath))
                {
                    try
                    {
                        LoadAsset(relativePath);
                    }
                    catch (System.Exception e)
                    {
                        Debug.LogError($"Could not load StateFlow asset at path: {relativePath}. Error: {e.Message}");
                    }
                }
            }
        }

        private void OnDragEnter(DragEnterEvent evt)
        {
            // Check if dragged paths contain .stateflow files
            if (HasStateFlowFiles())
            {
                DragAndDrop.visualMode = DragAndDropVisualMode.Copy;

                // Add visual feedback - highlight the welcome screen
                _welcomeScreen.style.backgroundColor = new Color(0.2f, 0.3f, 0.4f, 1f);
            }
        }

        private void OnDragLeave(DragLeaveEvent evt)
        {
            // Remove visual feedback
            _welcomeScreen.style.backgroundColor = new Color(0.15f, 0.15f, 0.15f, 1f);
        }

        private void OnDragUpdated(DragUpdatedEvent evt)
        {
            // Check if dragged paths contain .stateflow files
            if (HasStateFlowFiles())
            {
                DragAndDrop.visualMode = DragAndDropVisualMode.Copy;
            }
            else
            {
                DragAndDrop.visualMode = DragAndDropVisualMode.Rejected;
            }
        }

        private void OnDragPerform(DragPerformEvent evt)
        {
            // Remove visual feedback
            _welcomeScreen.style.backgroundColor = new Color(0.15f, 0.15f, 0.15f, 1f);

            // Find the first StateFlow file in the dragged paths
            var stateFlowPath = GetFirstStateFlowPath();
            if (!string.IsNullOrEmpty(stateFlowPath))
            {
                DragAndDrop.AcceptDrag();

                try
                {
                    LoadAsset(stateFlowPath);
                }
                catch (System.Exception e)
                {
                    Debug.LogError($"Failed to load StateFlow from drag-and-drop: {e.Message}");
                }
            }
        }

        private bool HasStateFlowFiles()
        {
            return GetFirstStateFlowPath() != null;
        }

        private string GetFirstStateFlowPath()
        {
            // Check paths first (for files dragged from project window)
            foreach (var path in DragAndDrop.paths)
            {
                if (path.EndsWith(".stateflow", System.StringComparison.OrdinalIgnoreCase))
                {
                    return path;
                }
            }

            // Check object references (for assets dragged from project window)
            foreach (var obj in DragAndDrop.objectReferences)
            {
                if (obj is TextAsset textAsset)
                {
                    var assetPath = AssetDatabase.GetAssetPath(textAsset);
                    if (assetPath.EndsWith(".stateflow", System.StringComparison.OrdinalIgnoreCase))
                    {
                        return assetPath;
                    }
                }
            }

            return null;
        }

        private void SetDirty(bool dirty)
        {
            _isDirty = dirty;
            UpdateWindowTitle();
        }

        private void UpdateWindowTitle()
        {
            if (_currentAsset != null)
            {
                var title = $"StateFlow Editor - {_currentAsset.AssetName}";
                if (_isDirty)
                    title += "*";
                titleContent = new GUIContent(title);
            }
            else
            {
                titleContent = new GUIContent("StateFlow Editor");
            }
        }

        private void UpdateFileNameDisplay()
        {
            if (_fileNameLabel != null)
            {
                if (_currentAsset != null)
                {
                    var displayName = !string.IsNullOrEmpty(_currentAssetPath) ? _currentAssetPath : _currentAsset.AssetName;
                    if (_isDirty)
                        displayName += "*";
                    _fileNameLabel.text = displayName;
                }
                else
                {
                    _fileNameLabel.text = "No file loaded";
                }
            }
        }

        private void SaveAsset()
        {
            if (_currentAsset == null)
                return;

            // If this is a new asset without a file path, prompt for save location
            // Default to .stateflow extension
            var path = EditorUtility.SaveFilePanel("Save StateFlow", "Assets", _currentAsset.AssetName + ".stateflow", "stateflow");
            if (!string.IsNullOrEmpty(path))
            {
                _currentAsset.SaveToFile(path);
                AssetDatabase.Refresh();
                Debug.Log($"Saved StateFlow: {_currentAsset.AssetName} to {path}");

                // Clear dirty flag and update displays
                SetDirty(false);
            }
        }

        private void SaveAssetAs()
        {
            if (_currentAsset == null)
                return;

            // Always prompt for save location
            var path = EditorUtility.SaveFilePanel("Save StateFlow As", "Assets", _currentAsset.AssetName + ".stateflow", "stateflow");
            if (!string.IsNullOrEmpty(path))
            {
                _currentAsset.SaveToFile(path);
                AssetDatabase.Refresh();
                Debug.Log($"Saved StateFlow: {_currentAsset.AssetName} to {path}");

                // Clear dirty flag and update displays
                SetDirty(false);
            }
        }

        private void CreateNewAsset()
        {
            var asset = new StateFlowAsset("New StateFlow");
            _currentAssetPath = null;
            _currentAsset = asset;
            LoadAsset(asset);
        }

        private void OnVariableChanged()
        {
            SetDirty(true);
        }

        public void OnGraphChanged()
        {
            SetDirty(true);
        }

        private void ToggleGrid()
        {
            if (_graphView != null && _gridToggleButton != null)
            {
                bool newVisibility = _gridToggleButton.value;
                _graphView.SetGridVisible(newVisibility);
            }
        }

        private void UpdateGridToggleButton()
        {
            if (_gridToggleButton != null && _graphView != null)
            {
                bool isVisible = _graphView.IsGridVisible();
                _gridToggleButton.SetValueWithoutNotify(isVisible);
            }
        }

        private void RegisterKeyboardShortcuts()
        {
            var root = rootVisualElement;
            root.RegisterCallback<KeyDownEvent>(OnKeyDown);
        }

        private void OnKeyDown(KeyDownEvent evt)
        {
            // Handle keyboard shortcuts
            if (evt.ctrlKey || evt.commandKey) // Support both Ctrl (Windows/Linux) and Cmd (Mac)
            {
                switch (evt.keyCode)
                {
                    case KeyCode.S:
                        evt.StopPropagation();
                        SaveAsset();
                        break;
                    case KeyCode.N:
                        evt.StopPropagation();
                        CreateNewAsset();
                        break;
                    case KeyCode.O:
                        evt.StopPropagation();
                        LoadAssetDialog();
                        break;
                }
            }
            else
            {
                switch (evt.keyCode)
                {
                    case KeyCode.F:
                        evt.StopPropagation();
                        FrameAll();
                        break;
                }
            }
        }

        private void FrameAll()
        {
            if (_graphView != null)
            {
                _graphView.FrameAll();
            }
        }

        private void OnDisable()
        {
            // Save any pending changes
            if (_currentAsset != null && _isDirty)
            {
                if (EditorUtility.DisplayDialog("Unsaved Changes",
                    "You have unsaved changes. Do you want to save before closing?",
                    "Save", "Don't Save"))
                {
                    SaveAsset();
                }
            }
        }
    }
}
